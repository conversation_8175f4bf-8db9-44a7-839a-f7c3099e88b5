# Digestive Health Notifications

This document describes the implementation of digestive health notifications using the Novu notification service in the CareMate Processor system.

## Overview

The digestive notification system allows healthcare providers to send automated notifications to patients regarding:
- Medication reminders (probiotics, digestive enzymes, etc.)
- Dietary advice and recommendations
- Symptom monitoring reminders
- Appointment reminders for gastroenterology visits
- Test results notifications
- Health tips and educational content

## Implementation Details

### Database Configuration

#### Notification Table
A new notification configuration has been added:

```sql
INSERT INTO notification (
  notification_id,
  name,
  schema,
  schema_column,
  description,
  status,
  language
) VALUES (
  uuid(),
  'Digestive Health Notification',
  'Patient',
  'patient_id',
  'Send digestive health notifications to patients for medication reminders, dietary advice, and health monitoring',
  'Active',
  'en-US'
);
```

#### Novu Configuration Table
The corresponding Novu workflow configuration:

```sql
INSERT INTO notification_novu (
  notification_novu_id,
  notification_id,
  workflow_id,
  subscriber_id,
  receiver_email,
  receiver_phone,
  receiver_first_name,
  receiver_last_name
) VALUES (
  uuid(),
  '<notification_id>',
  'digestive-health-notification',
  'patient_id',
  'email',
  'phone',
  'first_name',
  'last_name'
);
```

### Workflow Configuration

- **Workflow ID**: `digestive-health-notification`
- **Data Source**: `Patient` table
- **Primary Key**: `patient_id`
- **Subscriber ID**: Patient's `patient_id`
- **Receiver Fields**: 
  - Email: `email`
  - Phone: `phone`
  - First Name: `first_name`
  - Last Name: `last_name`

## Setup Instructions

### 1. Database Setup

Run the database seeders to add the digestive notification configuration:

```bash
# Run the setup script
node scripts/setup_digestive_notifications.js

# Or run seeders manually
npx sequelize-cli db:seed --seed 20250210061768-notification-seeder.js
npx sequelize-cli db:seed --seed 20250715084652-notification-novu-seeder.js
```

### 2. Environment Configuration

Ensure your environment has Novu properly configured:

```env
NOVU_ENABLED=true
NOVU_API_KEY=your_novu_api_key
NOVU_APP_ID=your_novu_app_id
NOVU_DIGEST_WORKFLOW_ID=digestive-health-notification
```

### 3. Novu Dashboard Setup

1. Log into your Novu dashboard
2. Create a new workflow with ID: `digestive-health-notification`
3. Configure email and/or SMS templates
4. Set up the workflow triggers and channels

## Usage Examples

### Triggering Digestive Notifications

#### Method 1: Using the Notification Queue

Send an event to the notification queue:

```javascript
const event = {
  event_id: "digestive_001",
  parent_id: "patient_123", // patient_id
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "medication_reminder",
      medication_name: "Probiotics",
      dosage: "1 capsule daily",
      reminder_time: "08:00 AM",
      patient_message: "Don't forget your daily probiotic!"
    }
  }
};

await sendToRabbitMQ(event);
```

#### Method 2: Direct Novu Service Call

```javascript
const novuService = require('./services/novu.service');

await novuService.sendNotification({
  workflowId: 'digestive-health-notification',
  subscriberId: 'patient_123',
  subscriberData: {
    subscriberId: 'patient_123',
    email: '<EMAIL>',
    phone: '+**********',
    firstName: 'John',
    lastName: 'Doe'
  },
  payload: {
    notification_type: 'dietary_advice',
    recommendation: 'Increase fiber intake',
    foods_to_include: ['oats', 'beans', 'apples'],
    patient_message: 'Here are dietary recommendations for better digestive health.'
  },
  transactionId: `digestive_${Date.now()}`
});
```

## Notification Types

### 1. Medication Reminders
```javascript
{
  notification_type: "medication_reminder",
  medication_name: "Probiotics",
  dosage: "1 capsule daily",
  reminder_time: "08:00 AM",
  instructions: "Take with food",
  patient_message: "Time for your digestive health medication!"
}
```

### 2. Dietary Advice
```javascript
{
  notification_type: "dietary_advice",
  advice_type: "fiber_intake",
  recommendation: "Increase daily fiber to 25-30 grams",
  foods_to_include: ["oats", "beans", "apples"],
  foods_to_avoid: ["processed foods", "spicy foods"],
  patient_message: "Dietary recommendations for better digestive health."
}
```

### 3. Symptom Monitoring
```javascript
{
  notification_type: "symptom_monitoring",
  symptoms_to_track: ["bloating", "abdominal pain"],
  monitoring_period: "7 days",
  instructions: "Log symptoms daily, rate 1-10",
  patient_message: "Time for your digestive health check-in."
}
```

### 4. Appointment Reminders
```javascript
{
  notification_type: "appointment_reminder",
  appointment_date: "2025-02-15",
  appointment_time: "10:30 AM",
  doctor_name: "Dr. Smith",
  department: "Gastroenterology",
  patient_message: "Upcoming gastroenterology appointment reminder."
}
```

## Testing

### Run the Test Suite

```bash
# Test the complete implementation
node test/test_digestive_notification.js

# Send sample events
node test/sample_digestive_event.js medication_reminder
node test/sample_digestive_event.js dietary_advice
node test/sample_digestive_event.js all
```

### Test Scenarios

The test suite covers:
1. ✅ Novu configuration validation
2. ✅ Database notification configuration check
3. ✅ Test patient data creation/verification
4. ✅ Notification generation through the standard flow
5. ✅ Direct Novu service calls

## Integration with Existing System

### Event Flow

```
Patient Data → Rule Engine → Notification Event → Notification Queue → 
Generate Notification Function → Novu Service → Patient Notification
```

### Caching

The system uses the existing caching mechanism:
- Notification configurations are cached for 1 hour
- Novu configurations are retrieved from the database as needed

### Error Handling

- Failed notifications are logged with trace IDs
- Retry mechanisms follow the existing RabbitMQ patterns
- Graceful degradation when Novu is unavailable

## Monitoring and Logging

All digestive notifications include:
- Unique trace IDs for end-to-end tracking
- Performance metrics
- Success/failure logging
- Transaction IDs for Novu correlation

## Security Considerations

- Patient data is handled according to HIPAA compliance
- Email and phone data is validated before sending
- Subscriber data is encrypted in transit
- API keys are stored securely in environment variables

## Future Enhancements

Potential improvements:
1. **Personalized Scheduling**: Time-zone aware medication reminders
2. **Symptom Correlation**: AI-powered symptom pattern analysis
3. **Integration with Wearables**: Real-time digestive health monitoring
4. **Multilingual Support**: Notifications in patient's preferred language
5. **Rich Media**: Include images, videos, or interactive content
