const logger = require("../config/logger");
const config = require("../config/config");
const novuService = require("../services/novu.service");
const generateNotification = require("../functions/generateNotification.function");
const models = require("../models");

// Test data for digestive notification
const testDigestiveNotificationData = {
  event_id: "test_digestive_001",
  parent_id: "test_patient_001", // This should match a patient_id in your database
  event_type: "digestive_health",
  params: {
    notification: "Digestive Health Notification",
    data: {
      medication_name: "Probiotics",
      dosage: "1 capsule daily",
      reminder_time: "08:00 AM",
      dietary_advice: "Avoid spicy foods and increase fiber intake",
      next_appointment: "2025-02-15",
      symptoms_to_monitor: "bloating, abdominal pain, irregular bowel movements"
    }
  }
};

// Test context
const testContext = {
  trace_id: "test_trace_digestive_001",
  functionData: null
};

async function testDigestiveNotification() {
  try {
    console.log("🧪 Starting Digestive Notification Test");
    console.log("=====================================");

    // Check if Novu is enabled
    if (!config.novu.enabled) {
      console.log("⚠️  Novu is disabled in configuration");
      console.log("💡 To enable Novu, set NOVU_ENABLED=true in your environment");
      return;
    }

    if (!config.novu.apiKey) {
      console.log("⚠️  Novu API key is not configured");
      console.log("💡 Set NOVU_API_KEY in your environment");
      return;
    }

    console.log("✅ Novu configuration is valid");

    // Test 1: Check if digestive notification exists in database
    console.log("\n📋 Test 1: Checking notification configuration...");
    
    const digestiveNotification = await models.Notification.findOne({
      where: { name: "Digestive Health Notification" },
      include: [{
        model: models.NotificationNovu,
        as: "novu_config"
      }]
    });

    if (!digestiveNotification) {
      console.log("❌ Digestive Health Notification not found in database");
      console.log("💡 Run the database seeders: npm run db");
      return;
    }

    console.log("✅ Digestive notification configuration found");
    console.log(`   - Notification ID: ${digestiveNotification.notification_id}`);
    console.log(`   - Schema: ${digestiveNotification.schema}`);
    console.log(`   - Schema Column: ${digestiveNotification.schema_column}`);

    // Test 2: Check if test patient exists
    console.log("\n👤 Test 2: Checking test patient data...");
    
    let testPatient = await models.Patient.findOne({
      where: { patient_id: testDigestiveNotificationData.parent_id }
    });

    if (!testPatient) {
      console.log("⚠️  Test patient not found, creating one...");
      
      // Create a test patient
      testPatient = await models.Patient.create({
        patient_id: testDigestiveNotificationData.parent_id,
        first_name: "John",
        last_name: "Doe",
        birth_date: new Date("1985-06-15"),
        email: "<EMAIL>",
        phone: "+**********",
        gender: 1, // Assuming 1 = Male
        active: true
      });
      
      console.log("✅ Test patient created");
    } else {
      console.log("✅ Test patient found");
    }

    console.log(`   - Patient ID: ${testPatient.patient_id}`);
    console.log(`   - Name: ${testPatient.first_name} ${testPatient.last_name}`);
    console.log(`   - Email: ${testPatient.email}`);
    console.log(`   - Phone: ${testPatient.phone}`);

    // Test 3: Test notification generation
    console.log("\n📧 Test 3: Testing notification generation...");
    
    try {
      await generateNotification(testDigestiveNotificationData, testContext);
      console.log("✅ Notification generation completed successfully");
    } catch (error) {
      console.log("❌ Notification generation failed:");
      console.log(`   Error: ${error.message}`);
      throw error;
    }

    // Test 4: Test direct Novu service call
    console.log("\n🔔 Test 4: Testing direct Novu service call...");
    
    const novuTestData = {
      workflowId: "digestive-health-notification",
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        ...testDigestiveNotificationData.params.data,
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_email: testPatient.email,
        notification_type: "digestive_health",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_test_${Date.now()}`
    };

    try {
      const result = await novuService.sendNotification(novuTestData);
      console.log("✅ Direct Novu notification sent successfully");
      console.log(`   Transaction ID: ${novuTestData.transactionId}`);
    } catch (error) {
      console.log("❌ Direct Novu notification failed:");
      console.log(`   Error: ${error.message}`);
      throw error;
    }

    console.log("\n🎉 All tests completed successfully!");
    console.log("=====================================");
    console.log("✅ Digestive notification implementation is working correctly");
    console.log("\n📝 Next steps:");
    console.log("   1. Set up the 'digestive-health-notification' workflow in your Novu dashboard");
    console.log("   2. Configure email/SMS templates for digestive health notifications");
    console.log("   3. Test with real patient data");
    console.log("   4. Set up triggers for digestive health events");

  } catch (error) {
    console.log("\n❌ Test failed:");
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

// Run the test
if (require.main === module) {
  testDigestiveNotification()
    .then(() => {
      console.log("\n🏁 Test execution completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Test execution failed:", error);
      process.exit(1);
    });
}

module.exports = testDigestiveNotification;
