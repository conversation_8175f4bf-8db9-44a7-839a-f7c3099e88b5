const logger = require("../config/logger");
const config = require("../config/config");
const { getCachedEventConfig, getCachedNotificationConfig, getCachedNovuNotification } = require("../helpers/caching.helper");
// const { sendToRabbitMQ } = require("../services/event.service");
const novuService = require("../services/novu.service");
const models = require("../models");


/**
 * Generates notifications based on event data and routes them to appropriate channels.
 *
 * @param {Object} event - The event instance containing notification parameters.
 * @param {string} event.event_id - Unique identifier for the event.
 * @param {string|number} event.parent_id - ID of the parent record.
 * @param {Object} event.params - Event parameters.
 * @param {string} event.params.notification - Name of the notification to generate.
 * @param {Object} context - Execution context with tracing information.
 * @param {string} context.trace_id - Unique trace identifier for logging.
 * @returns {Promise<void>} Resolves when notifications have been generated and routed.
 * @throws {Error} If notification configuration is not found or routing fails.
 */
const generateNotification = async (event, context) => {
    const traceId = context.trace_id;
    logger.info(`[TRACE ${traceId}] Starting notification generation`);
    try {
        // Handle both notification and notification_name for backward compatibility
        const notification_name = event.params.notification || event.params.notification_name;

        if (!notification_name) {
            throw new Error('Missing notification name in event.params.notification or event.params.notification_name');
        }

        if (config.novu && config.novu.enabled) {
            const novunotificationConfigArray = await getCachedNovuNotification(notification_name);

            if (!novunotificationConfigArray || novunotificationConfigArray.length === 0) {
                throw new Error(`No configuration found for notification named as: ${notification_name}`);
            }
            logger.info(`[TRACE ${traceId}] Novu is enabled, sending notification directly`);
            // Get the notification config to access schema information
            const novuConfig = novunotificationConfigArray;
            const { schema, schema_column } = novuConfig.notification;
            const Model = models[schema];
            const dbData = await Model.findOne({ where: { [schema_column]: event.parent_id } });
            console.log(dbData);
            if (!dbData) {
                throw new Error(`No data found in ${schema} for ${schema_column}: ${event.parent_id}`);
            }

            const templateData = { ...event.params.data, ...dbData.dataValues };

            // Get subscriber ID and email using column names from Novu config
            const subscriberId = dbData[novuConfig.subscriber_id];

            const subscriberData = {
                subscriberId: subscriberId,
                email: dbData[novuConfig.receiver_email],
                phone: dbData[novuConfig.receiver_phone],
                firstName: dbData[novuConfig.receiver_first_name],
                lastName: dbData[novuConfig.receiver_last_name] || 'LastName'
            };

            if (novuConfig.receiver_phone && dbData[novuConfig.receiver_phone]) {
                subscriberData.phone = dbData[novuConfig.receiver_phone];
            }
            if (!subscriberData.email && !subscriberData.phone) {
                throw new Error(`No email or phone found for subscriber in ${schema} with ${schema_column}: ${event.parent_id}`)
            }

            // Map notification_name to Novu workflow ID (kebab-case)
            const workflowId = novunotificationConfigArray[0]?.workflow_id ||
                notification_name.replace(/\s+/g, '-').toLowerCase();

            // Prepare payload with all available data
            const payload = {
                ...templateData,
                event_type: event.event_type,
                parent_id: event.parent_id,
                trace_id: traceId,
                channels: ['email']
            };

            // Send notification via Novu service directly
            const transactionId = `${event.event_id}_${Date.now()}`; 
            await novuService.sendDigestNotification({
                workflowId,
                subscriberId,
                subscriberData,
                payload,
                transactionId
            });
            logger.info(`[TRACE ${traceId}] Notification '${notification_name}' sent successfully via Novu`);
        } else {
            const notification_name = event.params.notification;
            const notificationConfigArray = await getCachedNotificationConfig(notification_name);

            if (!notificationConfigArray || notificationConfigArray.length === 0) {
                throw `No configuration found for notification named as: ${notification_name}`;
            }

            // Iterate over the notification config array
            for (const notificationConfig of notificationConfigArray) {
                const { channel } = notificationConfig;
                let { queue, order } = await getCachedEventConfig(channel)
                event.event_type = channel;
                event.queue = queue;
                event.order = order;
                event.notification = notificationConfig;
                await sendToRabbitMQ(event);
                logger.info(`[TRACE ${traceId}] Notification, '${notification_name}' generated for channel: ${channel}`);
            }

        }
    } catch (error) {
        logger.error(`[TRACE ${traceId}] Error generating notification: ${error.message}`);
        throw error;
    }
};

module.exports = generateNotification;
