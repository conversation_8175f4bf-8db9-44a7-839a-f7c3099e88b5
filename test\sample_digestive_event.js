const logger = require("../config/logger");
const { sendToRabbitMQ } = require("../services/event.service");

/**
 * Sample digestive health events that could trigger notifications
 * These represent different scenarios where digestive notifications would be useful
 */

// Sample event for medication reminder
const medicationReminderEvent = {
  event_id: `digestive_med_reminder_${Date.now()}`,
  parent_id: "test_patient_001", // Should match a patient_id
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "medication_reminder",
      medication_name: "Probiotics",
      dosage: "1 capsule daily",
      reminder_time: "08:00 AM",
      instructions: "Take with food to improve absorption",
      patient_message: "Don't forget to take your daily probiotic supplement!"
    }
  }
};

// Sample event for dietary advice
const dietaryAdviceEvent = {
  event_id: `digestive_diet_advice_${Date.now()}`,
  parent_id: "test_patient_001",
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "dietary_advice",
      advice_type: "fiber_intake",
      recommendation: "Increase daily fiber intake to 25-30 grams",
      foods_to_include: ["oats", "beans", "apples", "broccoli", "whole grains"],
      foods_to_avoid: ["processed foods", "excessive caffeine", "spicy foods"],
      patient_message: "Here are some dietary recommendations to improve your digestive health."
    }
  }
};

// Sample event for symptom monitoring reminder
const symptomMonitoringEvent = {
  event_id: `digestive_symptom_monitor_${Date.now()}`,
  parent_id: "test_patient_001",
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "symptom_monitoring",
      symptoms_to_track: ["bloating", "abdominal pain", "bowel movement frequency"],
      monitoring_period: "7 days",
      instructions: "Please log your symptoms daily and rate severity from 1-10",
      next_checkup: "2025-02-15",
      patient_message: "Time for your weekly digestive health check-in. Please log your symptoms."
    }
  }
};

// Sample event for appointment reminder
const appointmentReminderEvent = {
  event_id: `digestive_appointment_reminder_${Date.now()}`,
  parent_id: "test_patient_001",
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "appointment_reminder",
      appointment_date: "2025-02-15",
      appointment_time: "10:30 AM",
      doctor_name: "Dr. Smith",
      department: "Gastroenterology",
      preparation_instructions: "Fast for 12 hours before the appointment",
      patient_message: "Reminder: You have a gastroenterology appointment coming up."
    }
  }
};

// Sample event for test results notification
const testResultsEvent = {
  event_id: `digestive_test_results_${Date.now()}`,
  parent_id: "test_patient_001",
  event_type: "notification",
  queue: "notification_queue",
  order: 1,
  params: {
    notification: "Digestive Health Notification",
    data: {
      notification_type: "test_results",
      test_name: "Comprehensive Metabolic Panel",
      result_status: "Normal",
      key_findings: "All digestive markers within normal range",
      follow_up_required: false,
      next_test_date: "2025-08-15",
      patient_message: "Your recent digestive health test results are available."
    }
  }
};

/**
 * Function to send a sample digestive event to the notification queue
 */
async function sendSampleDigestiveEvent(eventType = 'medication_reminder') {
  try {
    let eventToSend;
    
    switch (eventType) {
      case 'medication_reminder':
        eventToSend = medicationReminderEvent;
        break;
      case 'dietary_advice':
        eventToSend = dietaryAdviceEvent;
        break;
      case 'symptom_monitoring':
        eventToSend = symptomMonitoringEvent;
        break;
      case 'appointment_reminder':
        eventToSend = appointmentReminderEvent;
        break;
      case 'test_results':
        eventToSend = testResultsEvent;
        break;
      default:
        throw new Error(`Unknown event type: ${eventType}`);
    }

    console.log(`🚀 Sending ${eventType} event to notification queue...`);
    console.log('Event data:', JSON.stringify(eventToSend, null, 2));
    
    // Send to RabbitMQ notification queue
    await sendToRabbitMQ(eventToSend);
    
    console.log(`✅ ${eventType} event sent successfully!`);
    console.log(`Event ID: ${eventToSend.event_id}`);
    
    return eventToSend;
    
  } catch (error) {
    console.error(`❌ Failed to send ${eventType} event:`, error.message);
    throw error;
  }
}

/**
 * Function to send all sample events
 */
async function sendAllSampleEvents() {
  const eventTypes = [
    'medication_reminder',
    'dietary_advice', 
    'symptom_monitoring',
    'appointment_reminder',
    'test_results'
  ];
  
  console.log('🧪 Sending all sample digestive events...');
  console.log('=========================================');
  
  for (const eventType of eventTypes) {
    try {
      await sendSampleDigestiveEvent(eventType);
      console.log(''); // Add spacing between events
      
      // Add a small delay between events
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`Failed to send ${eventType}:`, error.message);
    }
  }
  
  console.log('🎉 All sample events sent!');
}

// CLI interface
if (require.main === module) {
  const eventType = process.argv[2] || 'medication_reminder';
  
  if (eventType === 'all') {
    sendAllSampleEvents()
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('Failed to send events:', error);
        process.exit(1);
      });
  } else {
    sendSampleDigestiveEvent(eventType)
      .then(() => process.exit(0))
      .catch((error) => {
        console.error('Failed to send event:', error);
        process.exit(1);
      });
  }
}

module.exports = {
  sendSampleDigestiveEvent,
  sendAllSampleEvents,
  medicationReminderEvent,
  dietaryAdviceEvent,
  symptomMonitoringEvent,
  appointmentReminderEvent,
  testResultsEvent
};
