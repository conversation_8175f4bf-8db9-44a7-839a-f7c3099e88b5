const { execSync } = require('child_process');
const path = require('path');
const logger = require('../config/logger');

async function setupDigestiveNotifications() {
  try {
    console.log('🚀 Setting up Digestive Notifications...');
    console.log('=====================================');

    // Get the current working directory
    const projectRoot = path.resolve(__dirname, '..');
    
    console.log('📁 Project root:', projectRoot);
    
    // Run the notification seeder
    console.log('\n📋 Running notification seeder...');
    try {
      execSync('npx sequelize-cli db:seed --seed 20250210061768-notification-seeder.js', {
        cwd: projectRoot,
        stdio: 'inherit'
      });
      console.log('✅ Notification seeder completed');
    } catch (error) {
      console.log('⚠️  Notification seeder may have already run or failed:', error.message);
    }

    // Run the Novu notification seeder
    console.log('\n🔔 Running Novu notification seeder...');
    try {
      execSync('npx sequelize-cli db:seed --seed 20250715084652-notification-novu-seeder.js', {
        cwd: projectRoot,
        stdio: 'inherit'
      });
      console.log('✅ Novu notification seeder completed');
    } catch (error) {
      console.log('⚠️  Novu notification seeder may have already run or failed:', error.message);
    }

    console.log('\n🎉 Setup completed!');
    console.log('=====================================');
    console.log('✅ Digestive notification configuration has been added to the database');
    console.log('\n📝 What was added:');
    console.log('   1. Digestive Health Notification in the notification table');
    console.log('   2. Novu workflow configuration for digestive-health-notification');
    console.log('\n🔧 Configuration details:');
    console.log('   - Workflow ID: digestive-health-notification');
    console.log('   - Schema: Patient');
    console.log('   - Schema Column: patient_id');
    console.log('   - Subscriber ID: patient_id');
    console.log('   - Receiver fields: email, phone, first_name, last_name');
    
    console.log('\n🧪 To test the implementation, run:');
    console.log('   node test/test_digestive_notification.js');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Run setup if this script is executed directly
if (require.main === module) {
  setupDigestiveNotifications()
    .then(() => {
      console.log('\n🏁 Setup execution completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup execution failed:', error);
      process.exit(1);
    });
}

module.exports = setupDigestiveNotifications;
