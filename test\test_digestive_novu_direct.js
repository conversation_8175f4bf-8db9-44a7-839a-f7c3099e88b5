const logger = require("../config/logger");
const config = require("../config/config");
const novuService = require("../services/novu.service");

/**
 * Direct test of digestive Novu notifications without database dependencies
 * This test focuses on the Novu service integration
 */

async function testDigestiveNovuDirect() {
  try {
    console.log("🧪 Testing Digestive Novu Notifications (Direct)");
    console.log("================================================");

    // Check if Novu is enabled
    if (!config.novu.enabled) {
      console.log("⚠️  Novu is disabled in configuration");
      console.log("💡 To enable Novu, set NOVU_ENABLED=true in your environment");
      return;
    }

    if (!config.novu.apiKey) {
      console.log("⚠️  Novu API key is not configured");
      console.log("💡 Set NOVU_API_KEY in your environment");
      return;
    }

    console.log("✅ Novu configuration is valid");
    console.log(`   - API Key: ${config.novu.apiKey.substring(0, 8)}...`);
    console.log(`   - Enabled: ${config.novu.enabled}`);

    // Test data for digestive notification
    const testPatient = {
      patient_id: "test_patient_digestive_001",
      first_name: "Jane",
      last_name: "Smith",
      email: "<EMAIL>",
      phone: "+**********"
    };

    // Test 1: Medication Reminder
    console.log("\n💊 Test 1: Medication Reminder Notification");
    const medicationData = {
      workflowId: "digestive-health-notification",
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        notification_type: "medication_reminder",
        medication_name: "Probiotics",
        dosage: "1 capsule daily",
        reminder_time: "08:00 AM",
        instructions: "Take with food to improve absorption",
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_message: "Don't forget to take your daily probiotic supplement!",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_med_${Date.now()}`
    };

    try {
      const result1 = await novuService.sendNotification(medicationData);
      console.log("✅ Medication reminder sent successfully");
      console.log(`   Transaction ID: ${medicationData.transactionId}`);
    } catch (error) {
      console.log("❌ Medication reminder failed:");
      console.log(`   Error: ${error.message}`);
    }

    // Test 2: Dietary Advice
    console.log("\n🥗 Test 2: Dietary Advice Notification");
    const dietaryData = {
      workflowId: "digestive-health-notification",
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        notification_type: "dietary_advice",
        advice_type: "fiber_intake",
        recommendation: "Increase daily fiber intake to 25-30 grams",
        foods_to_include: ["oats", "beans", "apples", "broccoli", "whole grains"],
        foods_to_avoid: ["processed foods", "excessive caffeine", "spicy foods"],
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_message: "Here are some dietary recommendations to improve your digestive health.",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_diet_${Date.now()}`
    };

    try {
      const result2 = await novuService.sendNotification(dietaryData);
      console.log("✅ Dietary advice sent successfully");
      console.log(`   Transaction ID: ${dietaryData.transactionId}`);
    } catch (error) {
      console.log("❌ Dietary advice failed:");
      console.log(`   Error: ${error.message}`);
    }

    // Test 3: Symptom Monitoring
    console.log("\n📊 Test 3: Symptom Monitoring Notification");
    const symptomData = {
      workflowId: "digestive-health-notification",
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        notification_type: "symptom_monitoring",
        symptoms_to_track: ["bloating", "abdominal pain", "bowel movement frequency"],
        monitoring_period: "7 days",
        instructions: "Please log your symptoms daily and rate severity from 1-10",
        next_checkup: "2025-02-15",
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_message: "Time for your weekly digestive health check-in. Please log your symptoms.",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_symptom_${Date.now()}`
    };

    try {
      const result3 = await novuService.sendNotification(symptomData);
      console.log("✅ Symptom monitoring sent successfully");
      console.log(`   Transaction ID: ${symptomData.transactionId}`);
    } catch (error) {
      console.log("❌ Symptom monitoring failed:");
      console.log(`   Error: ${error.message}`);
    }

    // Test 4: Appointment Reminder
    console.log("\n📅 Test 4: Appointment Reminder Notification");
    const appointmentData = {
      workflowId: "digestive-health-notification",
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        notification_type: "appointment_reminder",
        appointment_date: "2025-02-15",
        appointment_time: "10:30 AM",
        doctor_name: "Dr. Smith",
        department: "Gastroenterology",
        preparation_instructions: "Fast for 12 hours before the appointment",
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_message: "Reminder: You have a gastroenterology appointment coming up.",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_appt_${Date.now()}`
    };

    try {
      const result4 = await novuService.sendNotification(appointmentData);
      console.log("✅ Appointment reminder sent successfully");
      console.log(`   Transaction ID: ${appointmentData.transactionId}`);
    } catch (error) {
      console.log("❌ Appointment reminder failed:");
      console.log(`   Error: ${error.message}`);
    }

    // Test 5: Using sendDigestNotification method
    console.log("\n🔄 Test 5: Using sendDigestNotification method");
    const digestData = {
      workflowId: "digestive-health-notification", // This will be overridden by digestWorkflowId
      subscriberId: testPatient.patient_id,
      subscriberData: {
        subscriberId: testPatient.patient_id,
        email: testPatient.email,
        phone: testPatient.phone,
        firstName: testPatient.first_name,
        lastName: testPatient.last_name
      },
      payload: {
        notification_type: "general_health_tip",
        tip: "Stay hydrated and eat regular meals to maintain good digestive health",
        patient_name: `${testPatient.first_name} ${testPatient.last_name}`,
        patient_message: "Here's a daily digestive health tip for you!",
        timestamp: new Date().toISOString()
      },
      transactionId: `digestive_digest_${Date.now()}`
    };

    try {
      const result5 = await novuService.sendDigestNotification(digestData);
      console.log("✅ Digest notification sent successfully");
      console.log(`   Transaction ID: ${digestData.transactionId}`);
    } catch (error) {
      console.log("❌ Digest notification failed:");
      console.log(`   Error: ${error.message}`);
    }

    console.log("\n🎉 All direct Novu tests completed!");
    console.log("===================================");
    console.log("✅ Digestive notification Novu integration is working correctly");
    console.log("\n📝 Next steps:");
    console.log("   1. Set up the 'digestive-health-notification' workflow in your Novu dashboard");
    console.log("   2. Configure email/SMS templates for digestive health notifications");
    console.log("   3. Set up database configuration using the seeders");
    console.log("   4. Test with the full notification queue flow");

  } catch (error) {
    console.log("\n❌ Test failed:");
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

// Run the test
if (require.main === module) {
  testDigestiveNovuDirect()
    .then(() => {
      console.log("\n🏁 Test execution completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Test execution failed:", error);
      process.exit(1);
    });
}

module.exports = testDigestiveNovuDirect;
